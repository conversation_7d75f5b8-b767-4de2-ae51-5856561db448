{"name": "travel-b2b-crm-portal", "version": "1.0.0", "description": "Production-level Travel B2B & CRM Portal using MERN Stack", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm start", "build": "cd client && npm run build", "start": "cd server && npm start", "test": "echo \"Error: no test specified\" && exit 1", "install-server": "cd server && npm install", "install-client": "cd client && npm install", "install-all": "npm install && npm run install-server && npm run install-client"}, "keywords": ["travel", "b2b", "crm", "mern", "react", "nodejs", "mongodb"], "author": "Travel Portal Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "type": "commonjs"}