{"name": "travel-b2b-crm-server", "version": "1.0.0", "description": "Backend server for Travel B2B & CRM Portal", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["travel", "b2b", "crm", "express", "mongodb"], "author": "Travel Portal Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "compression": "^1.7.4", "morgan": "^1.10.0", "nodemailer": "^6.9.4", "moment": "^2.29.4", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}}