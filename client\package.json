{"name": "travel-b2b-crm-client", "version": "1.0.0", "description": "Frontend client for Travel B2B & CRM Portal", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "react-scripts": "5.0.1", "axios": "^1.5.0", "react-query": "^3.39.3", "react-hook-form": "^7.45.4", "react-hot-toast": "^2.4.1", "react-icons": "^4.10.1", "recharts": "^2.8.0", "date-fns": "^2.30.0", "clsx": "^2.0.0", "react-datepicker": "^4.16.0", "react-select": "^5.7.4", "react-table": "^7.8.0"}, "devDependencies": {"@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "@types/node": "^20.5.7", "typescript": "^5.1.6", "tailwindcss": "^3.3.3", "autoprefixer": "^10.4.15", "postcss": "^8.4.29", "@tailwindcss/forms": "^0.5.6", "@tailwindcss/typography": "^0.5.10"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}